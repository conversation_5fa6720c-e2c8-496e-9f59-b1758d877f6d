/**
 * Chat Dock Component
 * Modern icon dock for chat actions with responsive design
 */

/* Chat Dock Container */
.chat-dock {
    position: fixed;
    bottom: 16px;
    left: 50%;
    transform: translateX(-50%);
    
    /* Mirror existing gradient and color scheme */
    background: linear-gradient(135deg, #1D1A2A 0%, #2C2738 100%);
    border-radius: 24px;
    border: 2px solid #3D3548;
    
    /* Dock layout */
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    
    /* Elevation and shadows */
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    
    /* Smooth transitions */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Ensure above other elements */
    z-index: 1000;
}

/* Dock expanded state */
.chat-dock.dock-expanded {
    bottom: 8px;
    padding: 12px 16px;
    gap: 12px;
    background: linear-gradient(135deg, #1D1A2A 0%, #2C2738 100%);
    border-color: #5BA9F9;
    box-shadow: 
        0 12px 48px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(91, 169, 249, 0.3);
}

/* Dock Icons */
.dock-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    
    /* Interactive styling */
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    
    /* Background and borders */
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    
    /* Smooth transitions */
    transition: all 0.2s ease;
    
    /* Icon styling */
    color: #B2AFC5;
    font-size: 20px;
}

.dock-icon:hover {
    background: rgba(91, 169, 249, 0.1);
    border-color: rgba(91, 169, 249, 0.3);
    color: #5BA9F9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(91, 169, 249, 0.2);
}

.dock-icon:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(91, 169, 249, 0.3);
}

/* Dock Icon States */
.dock-icon.active {
    background: rgba(91, 169, 249, 0.15);
    border-color: rgba(91, 169, 249, 0.5);
    color: #5BA9F9;
}

.dock-icon.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Dock Icon Types */
.dock-icon.new-chat {
    background: linear-gradient(135deg, rgba(91, 169, 249, 0.1) 0%, rgba(91, 169, 249, 0.05) 100%);
}

.dock-icon.new-chat:hover {
    background: linear-gradient(135deg, rgba(91, 169, 249, 0.2) 0%, rgba(91, 169, 249, 0.1) 100%);
}

.dock-icon.history {
    background: linear-gradient(135deg, rgba(156, 163, 175, 0.1) 0%, rgba(156, 163, 175, 0.05) 100%);
}

.dock-icon.history:hover {
    background: linear-gradient(135deg, rgba(156, 163, 175, 0.2) 0%, rgba(156, 163, 175, 0.1) 100%);
    color: #9CA3AF;
}

.dock-icon.clear {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
}

.dock-icon.clear:hover {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(239, 68, 68, 0.1) 100%);
    color: #EF4444;
    border-color: rgba(239, 68, 68, 0.3);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

.dock-icon.settings {
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.1) 0%, rgba(168, 85, 247, 0.05) 100%);
}

.dock-icon.settings:hover {
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.2) 0%, rgba(168, 85, 247, 0.1) 100%);
    color: #A855F7;
    border-color: rgba(168, 85, 247, 0.3);
    box-shadow: 0 4px 12px rgba(168, 85, 247, 0.2);
}

/* Dock Icon SVG */
.dock-icon svg {
    width: 20px;
    height: 20px;
    fill: currentColor;
    transition: transform 0.2s ease;
}

.dock-icon:hover svg {
    transform: scale(1.1);
}

/* Dock Separator */
.dock-separator {
    width: 1px;
    height: 24px;
    background: linear-gradient(to bottom, 
        transparent 0%, 
        rgba(255, 255, 255, 0.1) 50%, 
        transparent 100%);
    margin: 0 4px;
    opacity: 0.5;
}

/* Dock Badge */
.dock-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
    border: 2px solid #1D1A2A;
    font-size: 9px;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

/* Dock Tooltip */
.dock-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 8px;
    
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    
    /* Tooltip arrow */
    &::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 4px solid transparent;
        border-top-color: rgba(0, 0, 0, 0.9);
    }
}

.dock-icon:hover .dock-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-4px);
}

/* Mobile Responsive */
@media (max-width: 640px) {
    .chat-dock {
        bottom: 12px;
        padding: 6px 8px;
        gap: 6px;
        border-radius: 20px;
    }
    
    .dock-icon {
        width: 44px;
        height: 44px;
        border-radius: 10px;
    }
    
    .dock-icon svg {
        width: 18px;
        height: 18px;
    }
    
    .dock-expanded {
        bottom: 8px;
        padding: 8px 12px;
    }
    
    .dock-tooltip {
        display: none; /* Hide tooltips on mobile */
    }
}

@media (max-width: 480px) {
    .chat-dock {
        bottom: 8px;
        padding: 4px 6px;
        gap: 4px;
        border-radius: 16px;
    }
    
    .dock-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
    }
    
    .dock-icon svg {
        width: 16px;
        height: 16px;
    }
    
    .dock-separator {
        height: 20px;
        margin: 0 2px;
    }
}

/* Animation States */
.chat-dock.entering {
    animation: dockEnter 0.3s ease-out;
}

.chat-dock.leaving {
    animation: dockLeave 0.2s ease-in;
}

@keyframes dockEnter {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes dockLeave {
    from {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    to {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
}

/* Dock Icon Pulse Animation */
@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.dock-icon.pulse {
    animation: iconPulse 2s infinite;
}

/* Pro Feature Styling */
.dock-icon.pro-feature {
    position: relative;
}

.dock-icon.pro-feature::before {
    content: 'PRO';
    position: absolute;
    top: -2px;
    right: -2px;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #000;
    font-size: 8px;
    font-weight: 700;
    padding: 1px 4px;
    border-radius: 4px;
    z-index: 1;
}